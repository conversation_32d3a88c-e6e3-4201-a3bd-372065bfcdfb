/** @typedef {import('../types/AIsource.mjs').AIsource_t} AIsource_t */

import { loadAIsource, getAISourcesManager } from '../lib/AIsources_manager.mjs'
import { getPartInfo } from '../lib/locale.mjs'
import fs from 'node:fs'
import path from 'node:path'

/**
 * 统一配置文件路径
 */
const UNIFIED_CONFIG_PATH = './config/unified_ai_config.json'

/**
 * 加载统一配置文件
 * @returns {Object} 配置对象
 */
function loadUnifiedConfig() {
  try {
    const configText = fs.readFileSync(UNIFIED_CONFIG_PATH, 'utf-8')
    return JSON.parse(configText)
  } catch (error) {
    console.error(`❌ 读取统一配置文件失败:`, error.message)
    return { sources: {}, callingOrder: {} }
  }
}

/**
 * @type {Record<string, AIsource_t>}
 */
export let AIsources = {
	'detail-thinking': null,
	'web-browse': null,
	nsfw: null,
	sfw: null,
	expert: null,
	logic: null
}

/**
 * 存储调用顺序的配置
 */
let aiCallingOrder = {}

export function getAISourceData() {
	const result = {}
	for (const name in AIsources)
		result[name] = AIsources[name]?.filename || ''
	return result
}

export async function setAISourceData(data) {
	const newAIsources = {}
	for (const name in data) if (data[name])
		newAIsources[name] = loadAIsource(data[name])
	for (const name in newAIsources) newAIsources[name] = await newAIsources[name]
	AIsources = newAIsources
}

/**
 * 从统一配置文件获取AI调用顺序
 * @param {string} name 任务类型
 * @returns {string[]} AI源调用顺序
 */
export function GetAISourceCallingOrder(name) {
	// 从统一配置中获取调用顺序
	if (aiCallingOrder[name]) {
		return aiCallingOrder[name];
	}
	
	// 如果配置中没有找到，则使用默认顺序
	console.warn(`⚠️ 未在统一配置中找到 ${name} 的调用顺序，使用默认顺序`);
	
	// 默认调用顺序作为备份
	switch (name) {
		case 'detail-thinking':
			return ['detail-thinking', 'expert', 'sfw', 'web-browse', 'nsfw', 'logic']
		case 'web-browse':
			return ['web-browse', 'detail-thinking', 'expert', 'sfw', 'nsfw', 'logic']
		case 'expert':
			return ['expert', 'detail-thinking', 'sfw', 'web-browse', 'nsfw', 'logic']
		case 'sfw':
			return ['sfw', 'expert', 'detail-thinking', 'web-browse', 'nsfw', 'logic']
		case 'nsfw':
			return ['nsfw', 'logic', 'web-browse', 'sfw', 'expert', 'detail-thinking']
		case 'logic':
			return ['logic', 'nsfw', 'web-browse', 'sfw', 'expert', 'detail-thinking']
		default:
			return ['sfw', 'expert', 'detail-thinking', 'web-browse', 'nsfw', 'logic']
	}
}

export function noAISourceAvailable() {
	const result = Object.values(AIsources).every(x => !x)
	if (result) console.error('No AI source available:', AIsources)
	return result
}

export let last_used_AIsource

/**
 * @param {string} name
 * @param {(source:AIsource_t) => Promise<string>} caller
 * @param {number} trytimes
 * @param {(err: Error) => Promise<void>} error_logger
 * @returns {Promise<{content: string; files: {buffer: Buffer; name: string; mimeType: string; description: string}[]}>}
 */
export async function OrderedAISourceCalling(name, caller, trytimes = 3, error_logger = console.error) {
	// 调试输出所有可用的AI源
	console.log('======== AI源调试信息 ========');
	console.log(`请求的AI源类型: ${name}`);
	console.log('当前加载的所有AI源:');
	for (const [key, source] of Object.entries(AIsources)) {
		console.log(`  - ${key}: ${source ? source.name : '未加载'} (${source ? '可用' : '不可用'})`); 
	}
	
	// 调试输出调用顺序
	const callOrder = GetAISourceCallingOrder(name);
	console.log(`调用顺序配置: ${callOrder.join(' -> ')}`);
	
	// 获取可用的AI源列表
	const sources = [...new Set([...callOrder.map(x => AIsources[x]).filter(x => x), ...Object.values(AIsources).filter(x => x)])]
	
	console.log(`找到 ${sources.length} 个可用的AI源`);
	sources.forEach((source, index) => {
		console.log(`  ${index+1}. ${source.name} (配置类型: ${source.config?.type || '未知'}, 模型: ${source.config?.model || '未指定'})`);
	});
	console.log('==============================');
	
	// 如果没有可用的AI源，立即返回详细错误
	if (sources.length === 0) {
		console.error('❌ 没有可用的AI源!');
		throw new Error('没有可用的AI源，请检查配置！');
	}
	
	let lastErr = new Error('No AI source available')
	for (const source of sources) {
		for (let i = 0; i < trytimes; i++) {
			try {
				const sourceName = (await getPartInfo(last_used_AIsource = source))?.name || source.name;
				console.info(`OrderedAISourceCalling, attempting with: ${sourceName}, Attempt: ${i + 1}/${trytimes}`);
				console.log(`[OrderedAI_Debug] 调用AI源详情:`);
				console.log(`[OrderedAI_Debug] - 源名称: ${sourceName}`);
				console.log(`[OrderedAI_Debug] - 源类型: ${source.type}`);
				console.log(`[OrderedAI_Debug] - 配置类型: ${source.config?.type}`);
				console.log(`[OrderedAI_Debug] - 模型: ${source.config?.model}`);
				console.log(`[OrderedAI_Debug] - 端点: ${source.config?.endpoint || source.config?.url}`);
				console.log(`[OrderedAI_Debug] - 是否有fount生成器: ${!!source.fountGenerator}`);

				const result = await caller(source);

				console.log(`[OrderedAI_Debug] AI源调用成功，返回结果类型: ${typeof result}`);
				console.log(`[OrderedAI_Debug] 返回结果: ${JSON.stringify(result, null, 2).substring(0, 200)}...`);

				if (!result) {
					throw new Error(`AI源 ${sourceName} 返回了undefined或null结果`);
				}

				if (typeof result !== 'object') {
					throw new Error(`AI源 ${sourceName} 返回了非对象结果: ${typeof result}`);
				}

				if (!result.content && !result.files) {
					throw new Error(`AI源 ${sourceName} 返回结果缺少content和files字段`);
				}

				return result;
			} catch (err) {
				const sourceName = (await getPartInfo(source))?.name || source.name;
				console.error(`[DEBUG] OrderedAISourceCalling: Error caught during attempt ${i + 1} for source '${sourceName}'. Raw error:`, err);
				let currentErrorToLog;
				if (err instanceof Error) {
					currentErrorToLog = err;
				} else if (typeof err === 'object' && err !== null && typeof err.status === 'number' && typeof err.text === 'function') { // Looks like an HTTP Response object
					let responseBody = 'Could not read error response body.';
					try {
						responseBody = await err.text();
					} catch (textError) {
						console.error(`[DEBUG] Failed to read response body from error object for source '${sourceName}':`, textError);
					}
					currentErrorToLog = new Error(`API Error for source '${sourceName}': ${err.status} ${err.statusText || ''}. Response: ${responseBody.substring(0, 300)}`);
				} else {
					let stringifiedError = 'Could not stringify error.';
					try {
						stringifiedError = JSON.stringify(err);
					} catch (stringifyError) {
						// If stringify fails (e.g. circular object), use a placeholder
					}
					currentErrorToLog = new Error(`Unknown error during AI call for source '${sourceName}': ${stringifiedError}`);
				}
				lastErr = currentErrorToLog; // Ensure lastErr is always an Error object with a message
				await error_logger(currentErrorToLog); // Log the more informative error
			}
		}
	}

	console.error('❌ 所有AI源调用均失败!');
	throw lastErr
}

/**
 * 自动初始化 AI 源
 * 从统一配置文件加载所有启用的 AI 源和调用顺序
 */
export async function initAISources() {
    console.log('🤖 初始化 AI 源（使用统一配置 - 直接加载模式）...');
    AIsources = {}; // Reset/initialize global AIsources
    aiCallingOrder = {}; // Reset/initialize global aiCallingOrder

    try {
        const aiSourceManager = getAISourcesManager(); // Get the singleton instance of AISourcesManager
        aiSourceManager.sources.clear(); // Clear manager's internal state at the beginning
        console.log('[AIS_Index_Debug] AISourcesManager internal map cleared.');

        // Load unified configuration
        const unifiedConfig = loadUnifiedConfig(); 
        if (!unifiedConfig) {
            console.error('❌ 统一配置文件加载失败或为空，无法初始化AI源。');
            return;
        }
        console.log('📁 统一配置文件加载成功。');

        // Load calling order configuration
        if (unifiedConfig.callingOrder) {
            aiCallingOrder = unifiedConfig.callingOrder;
            console.log('✅ 加载 AI 调用顺序配置成功:');
            for (const [type, order] of Object.entries(aiCallingOrder)) {
                console.log(`   - ${type}: ${order.join(' -> ')}`);
            }
        } else {
            console.warn('⚠️ 统一配置中未找到调用顺序配置 (unifiedConfig.callingOrder)，将使用默认空对象。');
        }

        const sourcesFromUnifiedConfig = unifiedConfig.sources;
        if (!sourcesFromUnifiedConfig || typeof sourcesFromUnifiedConfig !== 'object' || Object.keys(sourcesFromUnifiedConfig).length === 0) {
            console.warn('⚠️ 统一配置中未找到有效的 "sources" 对象或 "sources" 为空。');
            return;
        }

        console.log(`🔍 发现 ${Object.keys(sourcesFromUnifiedConfig).length} 个AI源配置，开始直接处理...`);
        let enabledSourcesCount = 0;

        for (const [key, sourceConfigData] of Object.entries(sourcesFromUnifiedConfig)) {
            if (!sourceConfigData.enabled) {
                console.log(`🚫 AI源 '${key}' 已禁用，跳过加载。`);
                continue;
            }

            // Validate essential fields from unified_ai_config.json for this source
            if (!sourceConfigData.type) {
                console.error(`❌ 错误: AI源 '${key}' 在统一配置中缺少必需的 'type' 字段。跳过。`);
                continue;
            }
            if (!sourceConfigData.model) {
                console.error(`❌ 错误: AI源 '${key}' 在统一配置中缺少必需的 'model' 字段。跳过。`);
                continue;
            }
            if (!sourceConfigData.endpoint && (sourceConfigData.type === 'openai' || sourceConfigData.type === 'claude' || sourceConfigData.type === 'anthropic')) {
                 console.error(`❌ 错误: AI源 '${key}' (类型: ${sourceConfigData.type}) 在统一配置中缺少必需的 'endpoint' 字段。跳过。`);
                 continue;
            }

            try {
                const virtualFilename = `unified_config:${key}`; 
                
                const sourceInstance = aiSourceManager.createAISource(sourceConfigData, virtualFilename);
                await aiSourceManager.loadFountGenerator(sourceInstance);

                // IMPORTANT: Ensure the source is also registered with the AISourcesManager instance using the virtualFilename as the key
                aiSourceManager.sources.set(virtualFilename, sourceInstance);
                console.log(`[AIS_Index_Debug] AI源 '${key}' (virtualFilename: '${virtualFilename}') 已注册到 AISourcesManager。`);
                
                AIsources[key] = sourceInstance; // Populate the global AIsources map
                enabledSourcesCount++;
                console.log(`✅ AI源 '${key}' (${sourceConfigData.name || '未命名'}) 直接加载并初始化成功 (全局和管理器均已注册)。`);
            } catch (err) {
                console.error(`❌ AI源 '${key}' 直接加载或初始化失败:`, err.message, err.stack ? `\nStack: ${err.stack}` : '');
            }
        }

        if (enabledSourcesCount > 0) {
            console.log(`✅ 成功初始化 ${enabledSourcesCount} 个启用的 AI 源。`);
            // Display details of loaded AI sources
            console.log('======== 加载后的AI源详情 (直接加载模式) ========');
            for (const [type, source] of Object.entries(AIsources)) {
                if (source && source.config) { // Ensure source and source.config exist
                    console.log(`AI源Key: ${type}`);
                    console.log(`  名称: ${source.name || source.config.name}`);
                    console.log(`  Fount内部类型: ${source.type}`);
                    console.log(`  配置中的类型: ${source.config.type || '未指定'}`);
                    console.log(`  模型: ${source.config.model || '未指定'}`);
                    console.log(`  端点: ${source.config.endpoint || source.config.url || '未指定'}`);
                    console.log(`  API密钥: ${source.config.apiKey ? '已设置' : '未设置'}`);
                    console.log(`  配置来源(虚拟): ${source.filename}`);
                    console.log(`  完整配置对象 (source.config):`, JSON.stringify(source.config, null, 2).substring(0, 300) + '...');
                    console.log('  -------------------------');
                } else {
                    console.log(`❓ ${type}: 在AIsources中存在，但实例或配置不完整。`);
                }
            }
            console.log('=============================================');
        } else {
            console.log('ℹ️ 没有启用的AI源可供加载或初始化。');
        }

    } catch (error) {
        console.error('❌ 初始化 AI 源过程中发生严重错误:', error.message, error.stack ? `\nStack: ${error.stack}` : '');
        AIsources = {}; // Ensure AIsources is empty on critical failure
    }
}
